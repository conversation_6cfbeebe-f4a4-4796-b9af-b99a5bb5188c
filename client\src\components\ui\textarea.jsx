import * as React from "react";
import { cn } from "@/lib/utils";

function Textarea({ className, ...props }) {
  const [value, setValue] = React.useState("");

  const handleChange = (e) => {
    setValue(e.target.value);
    if (props.onChange) {
      props.onChange(e); // preserve external onChange if provided
    }
  };

  return (
    <textarea
      data-slot="textarea"
      value={value}
      onChange={handleChange}
      className={cn(
        "placeholder:text-blue-gray-500 focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-lg border bg-transparent px-3 py-2 text-base text-blue-gray-700 shadow-xs transition-[color,box-shadow,border-color] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        value === "" ? "border-blue-gray-500" : "border-blue-gray-700",
        className
      )}
      {...props}
    />
  );
}

export { Textarea };
