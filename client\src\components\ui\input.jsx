import * as React from "react";
import { cn } from "@/lib/utils";

function Input({ className, type, ...props }) {
  const [value, setValue] = React.useState("");

  const handleChange = (e) => {
    setValue(e.target.value);
    if (props.onChange) {
      props.onChange(e); // preserve external onChange if provided
    }
  };

  return (
    <input
      type={type}
      value={value}
      onChange={handleChange}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-blue-gray-500 dark:bg-input/30 border-input flex h-[46px] w-full min-w-0 rounded-lg border bg-transparent px-3 py-1 text-base text-blue-gray-700 shadow-xs transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
        value === "" ? "border-blue-gray-500" : "border-blue-gray-700",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className
      )}
      {...props}
    />
  );
}

export { Input };
