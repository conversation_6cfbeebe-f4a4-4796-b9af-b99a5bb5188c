import { useState, useEffect, useCallback } from "react";
import {
  Package,
  Search,
  Edit,
  Trash,
  Eye,
  MoreVertical,
  Loader2,
  AlertTriangle,
  DollarSign,
  Clock,
  TrendingUp,
  Image as ImageIcon,
  Filter,
  RefreshCw,
  X,
  Upload,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ScrollableDialog,
  ScrollableDialogContent,
  ScrollableDialogDescription,
  ScrollableDialogFooter,
  ScrollableDialogHeader,
  ScrollableDialogTitle,
} from "@/components/ui/scrollable-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Pagination } from "@/components/ui/pagination";
import { useFormik } from "formik";
import * as Yup from "yup";
import { inventoryService } from "@/services";
import { toast } from "sonner";
import { ripple } from "@/utils";

const InventoryManagement = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1); // UI uses 1-based pagination
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState(0);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [loadingAction, setLoadingAction] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const itemsPerPage = 6; // Match supplier portal

  // Format currency like supplier portal
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("fr-MA", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      useGrouping: true,
    }).format(amount);
  };

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch products from backend
  const fetchProducts = useCallback(
    async (isRefresh = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        const response = await inventoryService.getInventoryProducts({
          page: currentPage - 1, // Convert to 0-based for API
          size: itemsPerPage,
          sortBy: "label",
          sortDir: "asc",
          search: debouncedSearchQuery.trim() || undefined,
        });

        if (response.success) {
          const {
            content,
            totalPages: pages,
            totalElements: total,
          } = response.data;
          setProducts(content || []);
          setTotalPages(pages || 1);
          setTotalElements(total || 0);
        } else {
          toast.error("Failed to fetch inventory products");
          setProducts([]);
          setTotalPages(1);
          setTotalElements(0);
        }
      } catch (error) {
        console.error("Error fetching products:", error);
        toast.error("Failed to fetch inventory products");
        setProducts([]);
        setTotalPages(1);
        setTotalElements(0);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [currentPage, debouncedSearchQuery, itemsPerPage]
  );

  // Load products when component mounts or when search/pagination changes
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Reset to first page when search query changes
  useEffect(() => {
    if (debouncedSearchQuery !== searchQuery) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery, searchQuery]);

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchProducts(true);
  };

  // Image upload handlers
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ["image/jpeg", "image/png", "image/jpg"];
    if (!validTypes.includes(file.type)) {
      toast.error("Please select a valid image file (JPEG, JPG, or PNG)");
      return;
    }

    // Validate file size (max 8MB)
    const maxSize = 8 * 1024 * 1024; // 8MB in bytes
    if (file.size > maxSize) {
      toast.error("Image size should be less than 8MB");
      return;
    }

    setSelectedImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result);
    };
    reader.readAsDataURL(file);
  };

  const handleImageClick = () => {
    document.getElementById("image-upload").click();
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setUploadProgress(0);
    // Reset file input
    const fileInput = document.getElementById("image-upload");
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const uploadProductImage = async (file) => {
    if (!file) {
      console.warn("No file provided for upload");
      return null;
    }

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => {
        const newProgress = prev + Math.random() * 15;
        return newProgress >= 100 ? 100 : newProgress;
      });
    }, 200);

    try {
      const response = await inventoryService.uploadProductImage(file);

      if (!response || !response.success) {
        console.error(
          "Failed to upload image:",
          response?.message || "Unknown error"
        );
        toast.error(response?.message || "Failed to get image URL from server");
        return null;
      }

      setUploadProgress(100);
      // Return the image URL from the response
      return response.data || response;
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Failed to upload image. Please try again.");
      return null;
    } finally {
      clearInterval(progressInterval);
    }
  };

  // Handle view product details
  const handleViewProductDetails = (product) => {
    setSelectedProduct(product);
    setIsViewDialogOpen(true);
  };

  // Handle edit product
  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    editFormik.setValues({
      label: product.label || "",
      description: product.description || "",
      sellingPrice: product.sellingPrice || "",
      averageDeliveryTime: product.averageDeliveryTime || "",
    });

    // Set existing image preview if available
    if (product.imageUrl) {
      setImagePreview(product.imageUrl);
      setSelectedImage("existing"); // Mark that we have an existing image
    } else {
      setImagePreview(null);
      setSelectedImage(null);
    }
    setUploadProgress(0);

    setIsEditDialogOpen(true);
  };

  // Handle delete product
  const handleDeleteProduct = (product) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete product
  const confirmDeleteProduct = async () => {
    if (selectedProduct) {
      try {
        setLoadingAction(true);
        const response = await inventoryService.deleteInventoryProduct(
          selectedProduct.id
        );

        if (response.success) {
          toast.success("Product deleted successfully");
          setIsDeleteDialogOpen(false);
          fetchProducts();
        } else {
          toast.error(response.message || "Failed to delete product");
        }
      } catch (error) {
        console.error("Error deleting product:", error);
        toast.error("Failed to delete product");
      } finally {
        setLoadingAction(false);
      }
    }
  };

  // Product validation schema for full editing
  const ProductSchema = Yup.object().shape({
    label: Yup.string()
      .min(2, "Label must be at least 2 characters")
      .max(100, "Label must be less than 100 characters")
      .required("Product label is required"),
    description: Yup.string()
      .min(10, "Description must be at least 10 characters")
      .max(500, "Description must be less than 500 characters")
      .required("Product description is required"),
    sellingPrice: Yup.number()
      .min(0.01, "Selling price must be greater than 0")
      .required("Selling price is required"),
    averageDeliveryTime: Yup.number()
      .min(1, "Delivery time must be at least 1 day")
      .max(365, "Delivery time must be less than 365 days")
      .integer("Delivery time must be a whole number")
      .required("Delivery time is required"),
  });

  // Edit product form
  const editFormik = useFormik({
    initialValues: {
      label: "",
      description: "",
      sellingPrice: "",
      averageDeliveryTime: "",
    },
    validationSchema: ProductSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        setLoadingAction(true);

        // Upload image first if one is selected
        let updatedImageUrl = selectedProduct.imageUrl;
        if (selectedImage && selectedImage !== "existing") {
          const uploadedImageUrl = await uploadProductImage(selectedImage);
          if (uploadedImageUrl) {
            updatedImageUrl = uploadedImageUrl;
          } else {
            toast.error("Failed to upload image, keeping existing image");
          }
        }

        const response = await inventoryService.updateProduct(
          selectedProduct.id,
          {
            label: values.label,
            description: values.description,
            sellingPrice: parseFloat(values.sellingPrice),
            averageDeliveryTime: parseInt(values.averageDeliveryTime),
            imageUrl: updatedImageUrl,
          }
        );

        if (response.success) {
          toast.success("Product updated successfully");
          setIsEditDialogOpen(false);
          resetForm();
          // Reset image states
          setSelectedImage(null);
          setImagePreview(null);
          setUploadProgress(0);
          fetchProducts();
        } else {
          toast.error(response.message || "Failed to update product");
        }
      } catch (error) {
        console.error("Error updating product:", error);
        toast.error("Failed to update product");
      } finally {
        setLoadingAction(false);
      }
    },
  });

  return (
    <div className="space-y-4 sm:space-y-4">
      {/* Header Section */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Title and Description */}
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-blue-gray-50 rounded-lg">
                <Package className="h-6 w-6 text-blue-gray-700" />
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-blue-gray-900">
                  Inventory Management
                </h1>
                <p className="text-sm sm:text-base text-blue-gray-600 mt-1">
                  Manage your company's product inventory
                </p>
              </div>
            </div>

            {/* Stats */}
            {!loading && (
              <div className="flex items-center gap-4 text-sm text-blue-gray-600 mt-3">
                <span className="flex items-center gap-1">
                  <Package className="h-4 w-4" />
                  {totalElements} products
                </span>
                <span className="flex items-center gap-1">
                  <Filter className="h-4 w-4" />
                  Page {currentPage} of {totalPages}
                </span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3 self-start">
            <Button
              onMouseDown={(event) => ripple.create(event, "dark")}
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading || refreshing}
              className="py-3 border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </div>

        {/* Search Section */}
        <div className="mt-6">
          <div className="flex items-center w-full max-w-md border border-blue-gray-300 rounded-xl focus-within:border-blue-gray-800 focus-within:ring-2 focus-within:ring-blue-gray-100 transition-all h-[48px] sm:h-[53px]">
            <Search className="ml-3 size-4 sm:size-5 text-blue-gray-500 flex-shrink-0" />
            <input
              type="text"
              className="flex-1 h-full text-sm sm:text-base focus:outline-0 transition-all text-blue-gray-700 placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2"
              placeholder="Search products by name, description, or brand..."
              value={searchQuery}
              onChange={handleSearch}
            />
            {searchQuery && (
              <Button
                onMouseDown={(event) => ripple.create(event, "dark")}
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery("")}
                className="mr-2 h-8 w-8 p-0 hover:bg-blue-gray-100"
              >
                <X className="h-4 w-4 text-blue-gray-700" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {loading ? (
          // Loading state
          Array(itemsPerPage)
            .fill(0)
            .map((_, index) => (
              <Card key={index} className="animate-pulse overflow-hidden">
                <div className="h-40 sm:h-48 bg-blue-gray-100"></div>
                <div className="p-4 sm:p-5 space-y-3">
                  <div className="h-4 bg-blue-gray-100 rounded w-3/4"></div>
                  <div className="h-3 bg-blue-gray-100 rounded w-1/2"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-blue-gray-100 rounded w-full"></div>
                    <div className="h-3 bg-blue-gray-100 rounded w-2/3"></div>
                  </div>
                  <div className="h-8 bg-blue-gray-100 rounded"></div>
                </div>
              </Card>
            ))
        ) : products.length > 0 ? (
          // Products list
          products.map((product) => (
            <Card
              key={product.id}
              className="overflow-hidden border border-blue-gray-200 rounded-xl hover:shadow-lg transition-all duration-300 group bg-white"
            >
              <div className="relative">
                {/* Stock Indicator Badge */}
                <div className="absolute top-2 sm:top-3 left-2 sm:left-3 z-10">
                  <Badge
                    className={`px-2 py-1 text-xs font-medium shadow-sm ${
                      product.stockQuantity <= 0
                        ? "bg-red-50 border-red-200 text-red-700"
                        : product.stockQuantity < 5
                        ? "bg-yellow-50 border-yellow-200 text-yellow-700"
                        : "bg-green-50 border-green-200 text-green-700"
                    }`}
                  >
                    {product.stockQuantity <= 0
                      ? "Out of Stock"
                      : product.stockQuantity < 5
                      ? "Low Stock"
                      : `${product.stockQuantity} in stock`}
                  </Badge>
                </div>

                {/* Product Image */}
                <div className="w-full h-40 sm:h-48 bg-white flex items-center justify-center overflow-hidden border-b border-blue-gray-200">
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.label}
                      className="h-full w-full object-contain p-3 sm:p-4 group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center text-blue-gray-300">
                      <ImageIcon className="h-12 w-12 sm:h-16 sm:w-16" />
                      <p className="text-xs mt-2 hidden sm:block">
                        No image available
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-4 sm:p-5">
                {/* Product Header */}
                <div className="mb-3">
                  <h3 className="text-base sm:text-lg font-bold text-blue-gray-800 group-hover:text-blue-gray-700 transition-colors line-clamp-1">
                    {product.label}
                  </h3>
                  {product.brand && (
                    <p className="text-xs text-blue-gray-500 mt-1">
                      {product.brand}
                    </p>
                  )}
                </div>

                {/* Product Description - Truncated */}
                <p className="text-sm text-blue-gray-600 line-clamp-2 mb-4 min-h-[2.5rem]">
                  {product.description}
                </p>

                {/* Stats Row */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
                  <div className="flex items-center gap-1.5">
                    <Clock className="h-4 w-4 text-blue-gray-500 flex-shrink-0" />
                    <span className="text-xs font-medium text-blue-gray-700">
                      {product.averageDeliveryTime}{" "}
                      {product.averageDeliveryTime === 1 ? "day" : "days"}
                    </span>
                  </div>
                  <div className="flex items-center gap-1.5 bg-blue-gray-50 text-blue-gray-700 rounded-full py-1 px-2.5 self-start sm:self-auto">
                    <TrendingUp className="h-3.5 w-3.5 flex-shrink-0" />
                    <span className="text-xs font-semibold">
                      {product.margin?.toFixed(1) || 0}% margin
                    </span>
                  </div>
                </div>

                {/* Price Row - Responsive pricing layout */}
                <div className="relative border-t border-blue-gray-200 pt-4">
                  {/* Prices in a responsive layout */}
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-3 pr-10 sm:pr-12">
                    <div className="flex-1">
                      <div className="text-xs text-blue-gray-500 mb-1">
                        Purchase Price
                      </div>
                      <div className="flex items-baseline">
                        <div className="font-medium text-blue-gray-800 tabular-nums text-sm sm:text-base">
                          {formatCurrency(product.averageCostPrice || 0)}
                        </div>
                        <div className="text-blue-gray-500 text-xs ml-1">
                          MAD
                        </div>
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="text-xs text-blue-gray-500 mb-1">
                        Selling Price
                      </div>
                      <div className="flex items-baseline">
                        <div className="font-semibold text-blue-gray-800 tabular-nums text-sm sm:text-base">
                          {formatCurrency(product.sellingPrice)}
                        </div>
                        <div className="text-blue-gray-500 text-xs ml-1">
                          MAD
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions Dropdown - Positioned absolutely */}
                  <div className="absolute right-0 top-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 rounded-full hover:bg-blue-gray-50 transition-colors"
                        >
                          <MoreVertical className="h-4 w-4 text-blue-gray-600" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        className="border-blue-gray-200 shadow-lg"
                      >
                        <DropdownMenuItem
                          onClick={() => handleViewProductDetails(product)}
                          className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50 cursor-pointer"
                        >
                          <Eye className="h-4 w-4 mr-2" /> View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleEditProduct(product)}
                          className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50 cursor-pointer"
                        >
                          <Edit className="h-4 w-4 mr-2" /> Edit Product
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-blue-gray-100" />
                        <DropdownMenuItem
                          onClick={() => handleDeleteProduct(product)}
                          className="text-red-600 focus:text-red-600 focus:bg-red-50 cursor-pointer"
                        >
                          <Trash className="h-4 w-4 mr-2" /> Delete Product
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          // Empty state
          <div className="col-span-full py-12 text-center">
            <div className="bg-white p-6 sm:p-8 rounded-xl shadow-sm border border-blue-gray-200 max-w-md mx-auto">
              <div className="bg-blue-gray-50/60 rounded-full p-4 w-16 h-16 sm:w-20 sm:h-20 flex items-center justify-center mx-auto mb-6">
                <Package className="h-8 w-8 sm:h-10 sm:w-10 text-blue-gray-700" />
              </div>
              <h3 className="text-lg sm:text-xl font-medium text-blue-gray-800 mb-2">
                {searchQuery ? "No matching products" : "No products found"}
              </h3>
              <p className="text-sm sm:text-base text-blue-gray-600 mb-6">
                {searchQuery
                  ? "No products match your search criteria. Try a different search term or clear the search."
                  : "No products in inventory yet. Products will appear here after admin or employees make purchases from suppliers."}
              </p>
              {searchQuery && (
                <Button
                  onMouseDown={(event) => ripple.create(event, "dark")}
                  variant="outline"
                  className="border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
                  onClick={() => setSearchQuery("")}
                >
                  Clear Search
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!loading && products.length > 0 && totalPages > 1 && (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Pagination Info */}
            <div className="text-sm text-blue-gray-600 text-center sm:text-left">
              Showing{" "}
              <span className="font-medium">
                {Math.min(totalElements, (currentPage - 1) * itemsPerPage + 1)}
              </span>{" "}
              to{" "}
              <span className="font-medium">
                {Math.min(totalElements, currentPage * itemsPerPage)}
              </span>{" "}
              of <span className="font-medium">{totalElements}</span> products
            </div>

            {/* Pagination Controls */}
            <div className="flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.max(1, totalPages)}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        </div>
      )}

      {/* View Product Details Dialog */}
      <ScrollableDialog
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
      >
        <ScrollableDialogContent className="sm:max-w-2xl">
          <ScrollableDialogHeader>
            <ScrollableDialogTitle className="flex items-center gap-3 text-blue-gray-800">
              <div className="p-2 bg-blue-gray-50 rounded-lg">
                <Eye className="h-5 w-5 text-blue-gray-700" />
              </div>
              Product Details
            </ScrollableDialogTitle>
            <ScrollableDialogDescription className="text-blue-gray-600">
              View detailed information about this inventory product.
            </ScrollableDialogDescription>
          </ScrollableDialogHeader>

          {selectedProduct && (
            <div className="space-y-6">
              {/* Product Image */}
              <div className="w-full h-64 bg-white border border-blue-gray-100 rounded-xl flex items-center justify-center overflow-hidden">
                {selectedProduct.imageUrl ? (
                  <img
                    src={selectedProduct.imageUrl}
                    alt={selectedProduct.label}
                    className="max-w-full max-h-full object-contain p-4"
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center text-blue-gray-300">
                    <ImageIcon className="h-20 w-20" />
                    <p className="text-sm mt-3">No image available</p>
                  </div>
                )}
              </div>

              {/* Product Information */}
              <div className="grid gap-6">
                {/* Basic Info */}
                <div className="grid gap-4">
                  <div>
                    <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                      Product Name
                    </Label>
                    <p className="text-lg font-semibold text-blue-gray-900">
                      {selectedProduct.label}
                    </p>
                  </div>

                  {selectedProduct.brand && (
                    <div>
                      <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                        Brand
                      </Label>
                      <p className="text-blue-gray-800">
                        {selectedProduct.brand}
                      </p>
                    </div>
                  )}

                  <div>
                    <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                      Description
                    </Label>
                    <p className="text-blue-gray-800 leading-relaxed">
                      {selectedProduct.description}
                    </p>
                  </div>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="bg-blue-gray-50 p-4 rounded-xl">
                    <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                      Stock Quantity
                    </Label>
                    <p className="text-2xl font-bold text-blue-gray-900">
                      {selectedProduct.stockQuantity}
                    </p>
                    <p className="text-sm text-blue-gray-500">
                      units available
                    </p>
                  </div>

                  <div className="bg-blue-gray-50 p-4 rounded-xl">
                    <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                      Delivery Time
                    </Label>
                    <p className="text-2xl font-bold text-blue-gray-900">
                      {selectedProduct.averageDeliveryTime}
                    </p>
                    <p className="text-sm text-blue-gray-500">
                      {selectedProduct.averageDeliveryTime === 1
                        ? "day"
                        : "days"}
                    </p>
                  </div>
                </div>

                {/* Pricing Information */}
                <div className="border border-blue-gray-200 rounded-xl p-4">
                  <h4 className="text-lg font-semibold text-blue-gray-900 mb-4">
                    Pricing Information
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                        Purchase Price
                      </Label>
                      <p className="text-xl font-semibold text-blue-gray-800">
                        {formatCurrency(selectedProduct.averageCostPrice || 0)}{" "}
                        MAD
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                        Selling Price
                      </Label>
                      <p className="text-xl font-semibold text-blue-gray-800">
                        {formatCurrency(selectedProduct.sellingPrice)} MAD
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-blue-gray-600 mb-2 block">
                        Profit Margin
                      </Label>
                      <p className="text-xl font-semibold text-green-600">
                        {selectedProduct.margin?.toFixed(1) || 0}%
                      </p>
                    </div>
                  </div>
                </div>

                {/* Timestamps */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-blue-gray-500">
                  <div>
                    <Label className="text-sm font-medium text-blue-gray-600 mb-1 block">
                      Added to Inventory
                    </Label>
                    <p>
                      {new Date(
                        selectedProduct.createdDate
                      ).toLocaleDateString()}
                    </p>
                  </div>
                  {selectedProduct.lastModifiedDate && (
                    <div>
                      <Label className="text-sm font-medium text-blue-gray-600 mb-1 block">
                        Last Modified
                      </Label>
                      <p>
                        {new Date(
                          selectedProduct.lastModifiedDate
                        ).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </ScrollableDialogContent>
      </ScrollableDialog>

      {/* Edit Product Modal */}
      <ScrollableDialog
        open={isEditDialogOpen}
        onOpenChange={(open) => {
          setIsEditDialogOpen(open);
          if (!open) {
            // Reset image states when closing
            setSelectedImage(null);
            setImagePreview(null);
            setUploadProgress(0);
            editFormik.resetForm();
          }
        }}
      >
        <ScrollableDialogContent className="sm:max-w-lg">
          <ScrollableDialogHeader>
            <ScrollableDialogTitle className="flex items-center gap-3 text-blue-gray-800">
              <div className="p-2 bg-blue-gray-50 rounded-lg">
                <Edit className="h-5 w-5 text-blue-gray-700" />
              </div>
              Edit Product
            </ScrollableDialogTitle>
            <ScrollableDialogDescription className="text-blue-gray-600">
              Update the product information. All fields are required.
            </ScrollableDialogDescription>
          </ScrollableDialogHeader>

          <form onSubmit={editFormik.handleSubmit} className="space-y-6">
            {/* Product Label */}
            <div className="space-y-2">
              <Label
                htmlFor="label"
                className="text-sm font-medium text-blue-gray-700"
              >
                Product Name
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="label"
                name="label"
                placeholder="Enter product name"
                value={editFormik.values.label}
                onChange={editFormik.handleChange}
                onBlur={editFormik.handleBlur}
                className={
                  editFormik.touched.label && editFormik.errors.label
                    ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                    : "border-blue-gray-300 focus:border-blue-gray-500 focus:ring-blue-gray-200"
                }
              />
              {editFormik.touched.label && editFormik.errors.label && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4" />
                  {editFormik.errors.label}
                </p>
              )}
            </div>

            {/* Product Description */}
            <div className="space-y-2">
              <Label
                htmlFor="description"
                className="text-sm font-medium text-blue-gray-700"
              >
                Description
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter product description"
                rows={4}
                value={editFormik.values.description}
                onChange={editFormik.handleChange}
                onBlur={editFormik.handleBlur}
                className={
                  editFormik.touched.description &&
                  editFormik.errors.description
                    ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                    : "border-blue-gray-300 focus:border-blue-gray-500 focus:ring-blue-gray-200"
                }
              />
              {editFormik.touched.description &&
                editFormik.errors.description && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertTriangle className="h-4 w-4" />
                    {editFormik.errors.description}
                  </p>
                )}
            </div>

            {/* Product Image Upload */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-blue-gray-700">
                Product Image
              </Label>
              <div className="flex flex-col gap-4">
                {/* Hidden file input */}
                <input
                  id="image-upload"
                  type="file"
                  accept="image/jpeg,image/jpg,image/png"
                  onChange={handleImageChange}
                  className="hidden"
                />

                {/* Image Preview */}
                <div
                  className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-blue-50/50 transition-colors ${
                    imagePreview ? "border-blue-200" : "border-blue-200"
                  }`}
                  onClick={handleImageClick}
                  style={{ maxHeight: "200px", overflow: "hidden" }}
                >
                  {imagePreview ? (
                    <div className="relative">
                      <img
                        src={imagePreview}
                        alt="Product preview"
                        className="h-32 mx-auto object-contain rounded-md"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2 h-8 w-8 rounded-full bg-red-500 hover:bg-red-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveImage();
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-2">
                      <div className="bg-blue-100 rounded-full p-3 mb-2">
                        <Upload className="h-6 w-6 text-blue-600" />
                      </div>
                      <p className="text-sm text-blue-700">
                        Click to upload product image
                      </p>
                      <p className="text-xs text-blue-500">
                        JPEG, JPG or PNG (max 8MB)
                      </p>
                    </div>
                  )}
                </div>

                {/* Upload Progress */}
                {uploadProgress > 0 && uploadProgress < 100 && (
                  <div className="w-full bg-blue-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )}
              </div>
            </div>

            {/* Pricing and Delivery Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Selling Price */}
              <div className="space-y-2">
                <Label
                  htmlFor="sellingPrice"
                  className="text-sm font-medium text-blue-gray-700"
                >
                  Selling Price (MAD)
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="sellingPrice"
                  name="sellingPrice"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={editFormik.values.sellingPrice}
                  onChange={editFormik.handleChange}
                  onBlur={editFormik.handleBlur}
                  className={
                    editFormik.touched.sellingPrice &&
                    editFormik.errors.sellingPrice
                      ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                      : "border-blue-gray-300 focus:border-blue-gray-500 focus:ring-blue-gray-200"
                  }
                />
                {editFormik.touched.sellingPrice &&
                  editFormik.errors.sellingPrice && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertTriangle className="h-4 w-4" />
                      {editFormik.errors.sellingPrice}
                    </p>
                  )}
              </div>

              {/* Delivery Time */}
              <div className="space-y-2">
                <Label
                  htmlFor="averageDeliveryTime"
                  className="text-sm font-medium text-blue-gray-700"
                >
                  Delivery Time (days)
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="averageDeliveryTime"
                  name="averageDeliveryTime"
                  type="number"
                  min="1"
                  placeholder="1"
                  value={editFormik.values.averageDeliveryTime}
                  onChange={editFormik.handleChange}
                  onBlur={editFormik.handleBlur}
                  className={
                    editFormik.touched.averageDeliveryTime &&
                    editFormik.errors.averageDeliveryTime
                      ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                      : "border-blue-gray-300 focus:border-blue-gray-500 focus:ring-blue-gray-200"
                  }
                />
                {editFormik.touched.averageDeliveryTime &&
                  editFormik.errors.averageDeliveryTime && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertTriangle className="h-4 w-4" />
                      {editFormik.errors.averageDeliveryTime}
                    </p>
                  )}
              </div>
            </div>

            <ScrollableDialogFooter className="flex gap-3 pt-6 border-t border-blue-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={loadingAction}
                className="flex-1 border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loadingAction || !editFormik.isValid}
                className="flex-1 bg-blue-gray-800 hover:bg-blue-gray-700 text-white"
              >
                {loadingAction ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Edit className="h-4 w-4 mr-2" />
                    Update Product
                  </>
                )}
              </Button>
            </ScrollableDialogFooter>
          </form>
        </ScrollableDialogContent>
      </ScrollableDialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-gray-800">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              Delete Product
            </DialogTitle>
            <DialogDescription className="text-blue-gray-600">
              This action cannot be undone. This will permanently delete the
              product from your inventory.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-blue-gray-700">
              Are you sure you want to delete{" "}
              <span className="font-semibold">"{selectedProduct?.label}"</span>?
            </p>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={loadingAction}
              className="border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmDeleteProduct}
              disabled={loadingAction}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {loadingAction ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                "Delete Product"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InventoryManagement;
