package com.storeflow.services;

import com.storeflow.DTOs.SupplierDTO;
import com.storeflow.embeddables.SupplierProductId;
import com.storeflow.exception.DuplicateResourceException;
import com.storeflow.exception.FileUploadException;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.mappers.SupplierMapper;
import com.storeflow.mappers.SupplierProductMapper;
import com.storeflow.models.Product;
import com.storeflow.models.PurchaseItem;
import com.storeflow.models.Supplier;
import com.storeflow.models.SupplierProduct;
import com.storeflow.repositories.CompanyProductRepository;
import com.storeflow.repositories.ProductRepository;
import com.storeflow.repositories.PurchaseItemRepository;
import com.storeflow.repositories.SupplierProductRepository;
import com.storeflow.repositories.SupplierRepository;
import com.storeflow.requests.SupplierProductAdditionRequest;
import com.storeflow.requests.SupplierProductUpdateRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductService {
    private final UserService userService;
    private final FileService fileService;
    private final SupplierMapper supplierMapper;
    private final ProductRepository productRepository;
    private final SupplierRepository supplierRepository;
    private final SupplierProductRepository supplierProductRepository;
    private final CompanyProductRepository companyProductRepository;
    private final PurchaseItemRepository purchaseItemRepository;
    private final SupplierProductMapper supplierProductMapper;

    /**
     * Adds a new product for a supplier.
     *
     * @param connectedSupplier The authenticated supplier
     * @param productDetails The details of the product to be added
     * @return The UUID of the newly created product
     * @throws ResourceNotFoundException if the supplier profile is not found
     * @throws DuplicateResourceException if the supplier already has a product with the same label
     */
    @Transactional
    public UUID addProduct(Principal connectedSupplier, SupplierProductAdditionRequest productDetails) {
        // Get the authenticated supplier
        SupplierDTO supplier = (SupplierDTO) userService.getAuthenticatedUserProfileInfo(connectedSupplier);
        if (supplier == null) {
            log.error("Failed to retrieve supplier information for principal: {}", connectedSupplier.getName());
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Check if the supplier already has a product with the same label
        boolean productExists = supplierProductRepository.supplierHasProductWithLabel(
            supplier.id(), productDetails.label());

        if (productExists) {
            log.warn("Supplier {} attempted to add a duplicate product with label: {}",
                supplier.id(), productDetails.label());
            throw new DuplicateResourceException(
                "You already have a product with the label: " + productDetails.label() +
                    ". Use the update feature to modify it."
            );
        }

        log.info("Adding new product for supplier {} with label '{}'",
            supplier.id(), productDetails.label());

        // Create and save the new product with shared attributes
        Product product = Product.builder()
            .brand(productDetails.brand())
            .imageUrl(productDetails.imageUrl())
            .build();

        product = productRepository.save(product);
        log.debug("Created new base product with ID: {}", product.getId());

        // Create supplier-specific product information
        SupplierProduct supplierProduct = SupplierProduct.builder()
            .id(new SupplierProductId(supplier.id(), product.getId()))
            .supplier(supplierRepository.getReferenceById(supplier.id()))
            .product(product)
            .label(productDetails.label())
            .description(productDetails.description())
            .stockQuantity(productDetails.stockQuantity())
            .purchasePrice(productDetails.purchasePrice())
            .sellingPrice(productDetails.sellingPrice())
            .deliveryTime(productDetails.deliveryTime())
            .build();

        supplierProductRepository.save(supplierProduct);
        log.info("Successfully added product {} to supplier {}'s inventory",
            product.getId(), supplier.id());

        return product.getId();
    }

    /**
     * Updates an existing product for a supplier.
     *
     * @param connectedSupplier The authenticated supplier
     * @param productId The ID of the product to update
     * @param updateRequest The updated product details
     * @return The UUID of the updated product
     * @throws ResourceNotFoundException if the product doesn't exist or doesn't belong to the supplier
     * @throws DuplicateResourceException if updating the label would create a duplicate
     */
    @Transactional
    public UUID updateProduct(Principal connectedSupplier, UUID productId,
                              SupplierProductUpdateRequest updateRequest) {
        // Get the authenticated supplier
        SupplierDTO supplier = (SupplierDTO) userService.getAuthenticatedUserProfileInfo(connectedSupplier);
        if (supplier == null) {
            log.error("Failed to retrieve supplier information.");
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Find the supplier-product association
        SupplierProduct supplierProduct = supplierProductRepository
            .findBySupplierIdAndProductId(supplier.id(), productId)
            .orElseThrow(() -> {
                log.warn("Supplier {} attempted to update non-existent product {}",
                    supplier.id(), productId);
                return new ResourceNotFoundException(
                    "Product not found in your inventory with ID: " + productId);
            });

        // Check if label is being changed and if it would create a duplicate
        if (updateRequest.label() != null &&
            !updateRequest.label().equalsIgnoreCase(supplierProduct.getLabel())) {

            boolean duplicateLabelExists = supplierProductRepository.supplierHasProductWithLabel(
                supplier.id(), updateRequest.label());

            if (duplicateLabelExists) {
                log.warn("Supplier {} attempted to update product {} to a duplicate label: {}",
                    supplier.id(), productId, updateRequest.label());
                throw new DuplicateResourceException(
                    "You already have a product with the label: " + updateRequest.label());
            }

            // Update the supplier product label
            supplierProduct.setLabel(updateRequest.label());
            log.debug("Updated label for product {} to: {}", productId, updateRequest.label());
        }

        // Update supplier-product fields
        if (updateRequest.description() != null) {
            supplierProduct.setDescription(updateRequest.description());
            log.debug("Updated description for product {}", productId);
        }

        if (updateRequest.stockQuantity() != null) {
            supplierProduct.setStockQuantity(updateRequest.stockQuantity());
            log.debug("Updated stock quantity for product {} to: {}",
                productId, updateRequest.stockQuantity());
        }

        if (updateRequest.purchasePrice() != null) {
            supplierProduct.setPurchasePrice(updateRequest.purchasePrice());
            log.debug("Updated purchase price for product {} to: {}",
                productId, updateRequest.purchasePrice());
        }

        if (updateRequest.sellingPrice() != null) {
            supplierProduct.setSellingPrice(updateRequest.sellingPrice());
            log.debug("Updated selling price for product {} to: {}",
                productId, updateRequest.sellingPrice());
        }

        if (updateRequest.deliveryTime() != null) {
            supplierProduct.setDeliveryTime(updateRequest.deliveryTime());
            log.debug("Updated delivery time for product {} to: {} days",
                productId, updateRequest.deliveryTime());
        }

        // Update product fields
        Product product = supplierProduct.getProduct();

        if (updateRequest.brand() != null) {
            product.setBrand(updateRequest.brand());
            log.debug("Updated brand for product {} to: {}", productId, updateRequest.brand());
            productRepository.save(product);
        }

        if (updateRequest.imageUrl() != null) {
            product.setImageUrl(updateRequest.imageUrl());
            log.debug("Updated image URL for product {}", productId);
            productRepository.save(product);
        }

        // Save the updated supplier-product association
        supplierProductRepository.save(supplierProduct);
        log.info("Successfully updated product {} for supplier {}", productId, supplier.id());

        return productId;
    }

    /**
     * Deletes a product from a supplier's inventory.
     *
     * @param connectedSupplier The authenticated supplier
     * @param productId The ID of the product to delete
     * @throws ResourceNotFoundException if the product doesn't exist or doesn't belong to the supplier
     * @throws IllegalStateException if the product cannot be deleted due to business constraints
     */
    @Transactional
    public void deleteProduct(Principal connectedSupplier, UUID productId) {
        // Get the authenticated supplier
        SupplierDTO supplier = (SupplierDTO) userService.getAuthenticatedUserProfileInfo(connectedSupplier);
        if (supplier == null) {
            log.error("Failed to retrieve supplier information for principal.");
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Find the supplier-product association
        SupplierProduct supplierProduct = supplierProductRepository
            .findBySupplierIdAndProductId(supplier.id(), productId)
            .orElseThrow(() -> {
                log.warn("Supplier {} attempted to delete non-existent product {}",
                    supplier.id(), productId);
                return new ResourceNotFoundException(
                    "Product not found in your inventory with ID: " + productId);
            });

        log.info("Deleting product {} from supplier {}'s inventory", productId, supplier.id());

        // Delete the supplier-product association first
        supplierProductRepository.delete(supplierProduct);
        log.debug("Deleted supplier-product association for product: {}", productId);

        // Check if this was the last supplier for this product
        boolean hasOtherSuppliers = supplierProductRepository.existsByProductId(productId);

        // Only attempt to delete the base product if no other suppliers have it
        if (!hasOtherSuppliers) {
            // Check if the product is referenced by company inventory
            boolean isInCompanyInventory = companyProductRepository.existsByProductId(productId);

            // Check if the product is referenced by purchase items (historical data)
            List<PurchaseItem> purchaseItems = purchaseItemRepository.findByProductId(productId);
            boolean hasHistoricalPurchases = !purchaseItems.isEmpty();

            // Only delete the product if it's not referenced anywhere
            if (!isInCompanyInventory && !hasHistoricalPurchases) {
                log.info("Product {} has no other suppliers and no references, deleting base product", productId);
                productRepository.deleteById(productId);
            } else {
                // Log why the product is being preserved
                if (isInCompanyInventory) {
                    log.info("Product {} has no other suppliers but is still in company inventory, keeping base product", productId);
                }
                if (hasHistoricalPurchases) {
                    log.info("Product {} has no other suppliers but has historical purchase records, keeping base product", productId);
                }
            }
        } else {
            log.debug("Product {} still has other suppliers, keeping base product", productId);
        }

        log.info("Successfully deleted product {} from supplier {}'s inventory",
            productId, supplier.id());
    }

    /**
     * Retrieves all suppliers that offer a specific product.
     *
     * @param productId The ID of the product
     * @return A list of suppliers who offer the product
     * @throws ResourceNotFoundException if the product doesn't exist
     */
    @Transactional(readOnly = true)
    public List<SupplierDTO> getProductSuppliers(UUID productId) {
        // Verify the product exists
        if (!productRepository.existsById(productId)) {
            log.warn("Attempted to find suppliers for non-existent product {}", productId);
            throw new ResourceNotFoundException("Product not found with ID: " + productId);
        }

        log.debug("Retrieving suppliers for product {}", productId);

        // Get all supplier-product associations for this product
        List<SupplierProduct> supplierProducts = supplierProductRepository.findByProductId(productId);

        // Extract suppliers and map to DTOs using the existing mapper
        List<SupplierDTO> suppliers = supplierProducts.stream()
            .map(sp -> supplierMapper.toSupplierDTO(sp.getSupplier()))
            .collect(Collectors.toList());

        log.info("Found {} suppliers for product {}", suppliers.size(), productId);

        return suppliers;
    }

    /**
     * Retrieves all products for a supplier.
     *
     * @param connectedSupplier The authenticated supplier
     * @return A list of supplier products
     * @throws ResourceNotFoundException if the supplier profile is not found
     * @deprecated Use {@link #getSupplierProducts(Principal, Pageable)} instead for pagination
     */
    @Deprecated
    @Transactional(readOnly = true)
    public List<SupplierProduct> getSupplierProducts(Principal connectedSupplier) {
        // Get the authenticated supplier
        Supplier supplier = (Supplier) userService.getAuthenticatedUserProfileInfo(connectedSupplier);
        if (supplier == null) {
            log.error("Failed to retrieve supplier information");
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Retrieve all products for this supplier
        List<SupplierProduct> products = supplierProductRepository.findAllBySupplierId(supplier.getId());
        log.info("Retrieved {} products for supplier {}", products.size(), supplier.getId());

        return products;
    }

    /**
     * Retrieves all products for a supplier with pagination.
     *
     * @param connectedSupplier The authenticated supplier
     * @param pageable The pagination information
     * @return A page of supplier products
     * @throws ResourceNotFoundException if the supplier profile is not found
     */
    @Transactional(readOnly = true)
    public Page<SupplierProduct> getSupplierProducts(Principal connectedSupplier, Pageable pageable) {
        // Get the authenticated supplier
        SupplierDTO supplier = (SupplierDTO) userService.getAuthenticatedUserProfileInfo(connectedSupplier);

        if (supplier == null) {
            log.error("Failed to retrieve supplier information");
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Retrieve paginated products for this supplier
        Page<SupplierProduct> products = supplierProductRepository.findAllBySupplierId(supplier.id(), pageable);
        log.info("Retrieved page {} of {} products for supplier {}",
                pageable.getPageNumber(), products.getTotalElements(), supplier.id());

        return products;
    }

    /**
     * Searches for products of a supplier with pagination.
     *
     * @param connectedSupplier The authenticated supplier
     * @param searchQuery The search query to filter products
     * @param pageable The pagination information
     * @return A page of supplier products matching the search criteria
     * @throws ResourceNotFoundException if the supplier profile is not found
     */
    @Transactional(readOnly = true)
    public Page<SupplierProduct> searchSupplierProducts(Principal connectedSupplier, String searchQuery, Pageable pageable) {
        // Get the authenticated supplier
        Supplier supplier = (Supplier) userService.getAuthenticatedUserProfileInfo(connectedSupplier);
        if (supplier == null) {
            log.error("Failed to retrieve supplier information");
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Search for products with the given criteria
        Page<SupplierProduct> products;

        if (StringUtils.hasText(searchQuery)) {
            products = supplierProductRepository.searchBySupplierId(supplier.getId(), searchQuery, pageable);
            log.info("Found {} products matching '{}' for supplier {}",
                    products.getTotalElements(), searchQuery, supplier.getId());
        } else {
            products = supplierProductRepository.findAllBySupplierId(supplier.getId(), pageable);
            log.info("Retrieved all products for supplier {} due to empty search query", supplier.getId());
        }

        return products;
    }

    /**
     * Uploads a product image and updates the product's image URL.
     *
     * @param connectedSupplier The authenticated supplier
     * @param productId The ID of the product
     * @param imageFile The image file to upload
     * @return The URL of the uploaded image
     * @throws ResourceNotFoundException if the product doesn't exist or doesn't belong to the supplier
     * @throws FileUploadException if the file upload fails
     */
    @Transactional
    public String uploadProductImage(Principal connectedSupplier, UUID productId, MultipartFile imageFile) {
        // Get the authenticated supplier
        Supplier supplier = (Supplier) userService.getAuthenticatedUserProfileInfo(connectedSupplier);
        if (supplier == null) {
            log.error("Failed to retrieve supplier information");
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Find the supplier-product association
        SupplierProduct supplierProduct = supplierProductRepository
            .findBySupplierIdAndProductId(supplier.getId(), productId)
            .orElseThrow(() -> {
                log.warn("Supplier {} attempted to upload image for non-existent product {}",
                    supplier.getId(), productId);
                return new ResourceNotFoundException(
                    "Product not found in your inventory with ID: " + productId);
            });

        // Upload the image
        String imageUrl = fileService.uploadProductPicture(imageFile);
        log.info("Uploaded product image for product {} with URL: {}", productId, imageUrl);

        // Update the product's image URL
        Product product = supplierProduct.getProduct();
        product.setImageUrl(imageUrl);
        productRepository.save(product);

        return imageUrl;
    }

    /**
     * Retrieves a specific product from a supplier's inventory.
     *
     * @param connectedSupplier The authenticated supplier
     * @param productId The ID of the product
     * @return The supplier product
     * @throws ResourceNotFoundException if the product doesn't exist or doesn't belong to the supplier
     */
    @Transactional(readOnly = true)
    public SupplierProduct getSupplierProduct(Principal connectedSupplier, UUID productId) {
        // Get the authenticated supplier
        Supplier supplier = (Supplier) userService.getAuthenticatedUserProfileInfo(connectedSupplier);
        if (supplier == null) {
            log.warn("Failed to retrieve supplier information");
            throw new ResourceNotFoundException("Supplier profile not found");
        }

        // Find the supplier-product association
        return supplierProductRepository
            .findBySupplierIdAndProductId(supplier.getId(), productId)
            .orElseThrow(() -> {
                log.warn("Supplier {} attempted to access non-existent product {}",
                    supplier.getId(), productId);
                return new ResourceNotFoundException(
                    "Product not found in your inventory with ID: " + productId);
            });
    }

    /**
     * Retrieves all products for a specific supplier with pagination.
     *
     * @param supplierId The ID of the supplier
     * @param pageable The pagination information
     * @return A page of supplier products
     * @throws ResourceNotFoundException if the supplier doesn't exist
     */
    @Transactional(readOnly = true)
    public Page<SupplierProduct> getProductsBySupplierId(UUID supplierId, Pageable pageable) {
        // Verify the supplier exists
        if (!supplierRepository.existsById(supplierId)) {
            log.warn("Attempted to find products for non-existent supplier {}", supplierId);
            throw new ResourceNotFoundException("Supplier not found with ID: " + supplierId);
        }

        log.info("Retrieving products for supplier {} with pagination", supplierId);

        // Retrieve paginated products for this supplier
        Page<SupplierProduct> products = supplierProductRepository.findAllBySupplierId(supplierId, pageable);
        log.info("Retrieved page {} of {} products for supplier {}",
                pageable.getPageNumber(), products.getTotalElements(), supplierId);

        return products;
    }

    /**
     * Searches for products of a specific supplier with pagination.
     *
     * @param supplierId The ID of the supplier
     * @param searchQuery The search query to filter products
     * @param pageable The pagination information
     * @return A page of supplier products matching the search criteria
     * @throws ResourceNotFoundException if the supplier doesn't exist
     */
    @Transactional(readOnly = true)
    public Page<SupplierProduct> searchProductsBySupplierId(UUID supplierId, String searchQuery, Pageable pageable) {
        // Verify the supplier exists
        if (!supplierRepository.existsById(supplierId)) {
            log.warn("Attempted to search products for non-existent supplier {}", supplierId);
            throw new ResourceNotFoundException("Supplier not found with ID: " + supplierId);
        }

        log.info("Searching products for supplier {} with query: '{}'", supplierId, searchQuery);

        // Search for products with the given criteria
        Page<SupplierProduct> products;

        if (StringUtils.hasText(searchQuery)) {
            products = supplierProductRepository.searchBySupplierId(supplierId, searchQuery, pageable);
            log.info("Found {} products matching '{}' for supplier {}",
                    products.getTotalElements(), searchQuery, supplierId);
        } else {
            products = supplierProductRepository.findAllBySupplierId(supplierId, pageable);
            log.info("Retrieved all products for supplier {} due to empty search query", supplierId);
        }

        return products;
    }
}
