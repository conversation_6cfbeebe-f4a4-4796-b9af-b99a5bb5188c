package com.storeflow.mappers;

import com.storeflow.DTOs.CompanyProductDTO;
import com.storeflow.models.CompanyProduct;
import org.springframework.stereotype.Component;

/**
 * Mapper for CompanyProduct entity to DTO conversion.
 */
@Component
public class CompanyProductMapper {

    /**
     * Convert CompanyProduct entity to DTO.
     *
     * @param companyProduct The company product entity
     * @return The company product DTO
     */
    public CompanyProductDTO toDTO(CompanyProduct companyProduct) {
        if (companyProduct == null) {
            return null;
        }

        return new CompanyProductDTO(
            companyProduct.getId(),
            companyProduct.getProduct() != null ? companyProduct.getProduct().getId() : null,
            companyProduct.getLabel(),
            companyProduct.getDescription(),
            companyProduct.getProduct() != null ? companyProduct.getProduct().getBrand() : null,
            companyProduct.getProduct() != null ? companyProduct.getProduct().getImageUrl() : null,
            companyProduct.getStockQuantity(),
            companyProduct.getAverageCostPrice(),
            companyProduct.getSellingPrice(),
            companyProduct.getMargin(),
            companyProduct.getAverageDeliveryTime(),
            companyProduct.getCreatedDate(),
            companyProduct.getLastModifiedDate()
        );
    }
}
