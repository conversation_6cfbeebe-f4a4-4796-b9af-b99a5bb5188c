package com.storeflow.controllers;

import com.storeflow.DTOs.UserDTO;
import com.storeflow.enums.Role;
import com.storeflow.exception.AuthenticationFailedException;
import com.storeflow.exception.DuplicateResourceException;
import com.storeflow.models.Client;
import com.storeflow.models.User;
import com.storeflow.repositories.ClientRepository;
import com.storeflow.repositories.UserRepository;
import com.storeflow.requests.AuthenticationRequest;
import com.storeflow.requests.CompleteGoogleRegistrationRequest;
import com.storeflow.requests.ForgotPasswordRequest;
import com.storeflow.requests.RefreshTokenRequest;
import com.storeflow.requests.ResetPasswordRequest;
import com.storeflow.requests.VerifyCodeRequest;
import com.storeflow.services.*;
import com.storeflow.responses.ApiResponse;
import com.storeflow.responses.LoginResponse;
import com.storeflow.responses.RegisterRequest;
import com.storeflow.responses.RegistrationResponse;
import com.storeflow.utils.PhoneNumberFormatter;
import com.storeflow.utils.SecurePasswordGenerator;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;

@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
public class AuthenticationController {
    private final FileService fileService;
    private final EmailService emailService;
    private final ClientRepository clientRepository;
    private final SecurePasswordGenerator securePasswordGenerator;
    private final PasswordEncoder passwordEncoder;
    private final UserService userService;
    private final AuthenticationService authService;
    private final PasswordResetService passwordResetService;
    private final PhoneNumberFormatter phoneNumberFormatter;
    private final UserRepository userRepository;
    private final JwtService jwtService;

    @PostMapping("/register")
    public ResponseEntity<RegistrationResponse> register(
        @Valid @RequestBody RegisterRequest request
    ) {
        log.info("Registration request received for email: {}", request.email());
        RegistrationResponse response = authService.register(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(
        @Valid @RequestBody AuthenticationRequest request
    ) {
        log.info("Authentication request received for email: {}", request.getEmail());
        LoginResponse response = authService.login(request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/verify")
    public ResponseEntity<ApiResponse<Object>> verifyToken(Principal principal) {
        Object userInfo = userService.getAuthenticatedUserProfileInfo(principal);

        ApiResponse<Object> response = ApiResponse.builder()
            .success(true)
            .message("User profile retrieved successfully")
            .data(userInfo)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @PostMapping("/refresh-token")
    public ResponseEntity<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        return ResponseEntity.ok(authService.refreshToken(request.getRefreshToken()));
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<ApiResponse<Void>> forgotPassword(
        @Valid @RequestBody ForgotPasswordRequest request
    ) {
        log.info("Forgot password request received for email: {}", request.email());
        passwordResetService.processForgotPasswordRequest(request);

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(true)
            .message("If an account with this email exists, a verification code has been sent.")
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @PostMapping("/verify-reset-code")
    public ResponseEntity<ApiResponse<Boolean>> verifyResetCode(
        @Valid @RequestBody VerifyCodeRequest request
    ) {
        log.info("Verification code check request received for email: {}", request.email());
        boolean isValid = passwordResetService.verifyResetCode(request.email(), request.verificationCode());

        ApiResponse<Boolean> response = ApiResponse.<Boolean>builder()
            .success(true)
            .message(isValid ? "Verification code is valid" : "Invalid or expired verification code")
            .data(isValid)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<Void>> resetPassword(
        @Valid @RequestBody ResetPasswordRequest request
    ) {
        log.info("Password reset request received for email: {}", request.email());
        passwordResetService.resetPassword(request);

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(true)
            .message("Password has been reset successfully. You can now login with your new password.")
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @PostMapping("/oauth2/complete-registration")
    public ResponseEntity<ApiResponse<LoginResponse>> completeGoogleRegistration(
        @Valid @RequestBody CompleteGoogleRegistrationRequest request
    ) {
        try {
            log.info("Creating new Google OAuth user with email: {}", request.email());

            // Check if user already exists by email
            if (userRepository.existsByEmail(request.email())) {
                throw new DuplicateResourceException("An account with this email already exists");
            }

            // Check if user already exists by Google ID
            if (userRepository.findByGoogleId(request.googleId()).isPresent()) {
                throw new DuplicateResourceException("An account with this Google ID already exists");
            }

            // Format and validate phone number
            String formattedPhoneNumber = phoneNumberFormatter.formatToInternational(request.phoneNumber());

            // Check if phone number already exists
            if (userRepository.existsByPhoneNumber(formattedPhoneNumber)) {
                throw new DuplicateResourceException("Phone number already exists");
            }

            // Generate secure password for OAuth user
            String securePassword = securePasswordGenerator.generateSecurePassword();

            // Download and upload Google profile picture to our storage
            String profilePictureUrl;
            if (request.profilePictureUrl() != null && !request.profilePictureUrl().trim().isEmpty()) {
                log.info("Downloading Google profile picture for user: {}", request.email());
                profilePictureUrl = fileService.downloadAndUploadGoogleProfilePicture(request.profilePictureUrl());
            } else {
                log.info("No Google profile picture provided, using default for user: {}", request.email());
                profilePictureUrl = fileService.getDefaultProfilePictureUrl();
            }

            // Create new user with Google OAuth data
            User newUser = User.builder()
                .firstName(request.firstName().trim())
                .lastName(request.lastName().trim())
                .email(request.email().trim())
                .phoneNumber(formattedPhoneNumber)
                .password(passwordEncoder.encode(securePassword))
                .googleId(request.googleId())
                .role(Role.CLIENT)
                .profilePictureUrl(profilePictureUrl)
                .build();

            userRepository.save(newUser);
            log.info("Created new Google OAuth user with ID: {}", newUser.getId());

            // Create client entity
            Client client = Client.builder()
                .user(newUser)
                .build();
            clientRepository.save(client);
            log.info("Created client entity for user: {}", newUser.getId());

            // Send password via email
            emailService.sendPasswordEmail(newUser.getEmail(), securePassword);

            // Generate tokens
            String accessToken = jwtService.generateToken(newUser);
            String refreshToken = jwtService.generateRefreshToken(newUser);

            authService.saveUserToken(newUser, accessToken);

            LoginResponse loginResponse = LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .build();

            ApiResponse<LoginResponse> response = ApiResponse.<LoginResponse>builder()
                .success(true)
                .message("Registration completed successfully! Your password has been sent to your email.")
                .data(loginResponse)
                .build();

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error completing Google registration: {}", e.getMessage(), e);

            ApiResponse<LoginResponse> response = ApiResponse.<LoginResponse>builder()
                .success(false)
                .message(e.getMessage())
                .data(null)
                .build();

            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
}
