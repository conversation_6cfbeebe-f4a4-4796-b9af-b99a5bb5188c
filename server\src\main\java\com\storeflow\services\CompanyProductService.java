package com.storeflow.services;

import com.storeflow.DTOs.CompanyProductDTO;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.mappers.CompanyProductMapper;
import com.storeflow.models.CompanyProduct;
import com.storeflow.models.Product;
import com.storeflow.repositories.CompanyProductRepository;
import com.storeflow.repositories.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for managing company products (internal inventory).
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyProductService {

    private final CompanyProductRepository companyProductRepository;
    private final CompanyProductMapper companyProductMapper;
    private final ProductRepository productRepository;

    /**
     * Add stock to company inventory from a purchase.
     *
     * @param product The product
     * @param label The product label
     * @param description The product description
     * @param quantity The quantity purchased
     * @param costPrice The cost price per unit
     * @param sellingPrice The selling price per unit
     * @param deliveryTime The delivery time
     * @return The updated or created company product
     */
    @Transactional
    public CompanyProduct addStockFromPurchase(Product product, String label, String description,
                                             Integer quantity, BigDecimal costPrice,
                                             BigDecimal sellingPrice, Integer deliveryTime) {
        log.info("Adding stock to company inventory: {} units of {}", quantity, label);

        Optional<CompanyProduct> existingProduct = companyProductRepository.findByLabel(label);

        if (existingProduct.isPresent()) {
            // Update existing company product
            CompanyProduct companyProduct = existingProduct.get();
            companyProduct.addStock(quantity, costPrice);

            // Update selling price and delivery time (use latest values)
            companyProduct.setSellingPrice(sellingPrice);
            companyProduct.setAverageDeliveryTime(deliveryTime);
            companyProduct.setDescription(description); // Update description in case it changed

            log.info("Updated existing company product: {} (new stock: {})",
                label, companyProduct.getStockQuantity());

            return companyProductRepository.save(companyProduct);
        } else {
            // Create new company product
            CompanyProduct companyProduct = CompanyProduct.builder()
                .product(product)
                .label(label)
                .description(description)
                .stockQuantity(quantity)
                .averageCostPrice(costPrice)
                .sellingPrice(sellingPrice)
                .averageDeliveryTime(deliveryTime)
                .build();

            log.info("Created new company product: {} with {} units", label, quantity);

            return companyProductRepository.save(companyProduct);
        }
    }

    /**
     * Remove stock from company inventory (when clients order).
     *
     * @param label The product label
     * @param quantity The quantity to remove
     * @return true if successful, false if insufficient stock
     */
    @Transactional
    public boolean removeStockFromOrder(String label, Integer quantity) {
        log.info("Removing {} units of '{}' from company inventory", quantity, label);

        Optional<CompanyProduct> companyProductOpt = companyProductRepository.findByLabel(label);

        if (companyProductOpt.isEmpty()) {
            log.error("Company product not found with label: '{}'", label);
            return false;
        }

        CompanyProduct companyProduct = companyProductOpt.get();
        log.info("Found company product: '{}' with current stock: {}",
            companyProduct.getLabel(), companyProduct.getStockQuantity());

        boolean success = companyProduct.removeStock(quantity);

        if (success) {
            companyProductRepository.save(companyProduct);
            log.info("Successfully removed {} units of '{}' (remaining: {})",
                quantity, label, companyProduct.getStockQuantity());
        } else {
            log.error("Insufficient stock for '{}': requested {}, available {}",
                label, quantity, companyProduct.getStockQuantity());
        }

        return success;
    }

    /**
     * Get all company products with stock greater than zero.
     *
     * @return List of available company products
     */
    @Transactional(readOnly = true)
    public List<CompanyProduct> getAvailableProducts() {
        return companyProductRepository.findByStockQuantityGreaterThan(0);
    }

    /**
     * Get a company product by label.
     *
     * @param label The product label
     * @return The company product or null if not found
     */
    @Transactional(readOnly = true)
    public CompanyProduct getProductByLabel(String label) {
        return companyProductRepository.findByLabel(label).orElse(null);
    }

    /**
     * Get a company product by ID.
     *
     * @param productId The company product ID
     * @return The company product
     * @throws ResourceNotFoundException if the product doesn't exist
     */
    @Transactional(readOnly = true)
    public CompanyProduct getCompanyProductById(UUID productId) {
        return companyProductRepository.findById(productId)
            .orElseThrow(() -> {
                log.error("Company product not found with ID: {}", productId);
                return new ResourceNotFoundException("Company product not found with ID: " + productId);
            });
    }

    /**
     * Get a company product by label with stock check.
     *
     * @param label The product label
     * @return The company product if available, null otherwise
     */
    @Transactional(readOnly = true)
    public CompanyProduct getAvailableProductByLabel(String label) {
        return companyProductRepository.findByLabelAndStockQuantityGreaterThan(label, 0).orElse(null);
    }

    /**
     * Get all company products with pagination.
     *
     * @param pageable The pagination information
     * @return Page of company products
     */
    @Transactional(readOnly = true)
    public Page<CompanyProduct> getAllProducts(Pageable pageable) {
        return companyProductRepository.findAll(pageable);
    }

    /**
     * Search company products by label, description, or brand.
     *
     * @param searchTerm The search term
     * @param pageable The pagination information
     * @return Page of matching company products
     */
    @Transactional(readOnly = true)
    public Page<CompanyProduct> searchProducts(String searchTerm, Pageable pageable) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return getAllProducts(pageable);
        }
        return companyProductRepository.searchByLabelOrDescriptionOrBrand(searchTerm.trim(), pageable);
    }

    /**
     * Update a company product with full details.
     *
     * @param productId The company product ID
     * @param updateRequest The update request with new values
     * @return The updated company product
     * @throws ResourceNotFoundException if the product doesn't exist
     */
    @Transactional
    public CompanyProduct updateCompanyProduct(UUID productId, Object updateRequest) {
        CompanyProduct companyProduct = companyProductRepository.findById(productId)
            .orElseThrow(() -> {
                log.error("Company product not found with ID: {}", productId);
                return new ResourceNotFoundException("Company product not found with ID: " + productId);
            });

        log.info("Updating company product '{}' with new details", companyProduct.getLabel());

        // Use reflection to get the fields from the update request
        try {
            Class<?> requestClass = updateRequest.getClass();

            // Update label if provided
            try {
                Object label = requestClass.getMethod("label").invoke(updateRequest);
                if (label != null && !((String) label).trim().isEmpty()) {
                    companyProduct.setLabel((String) label);
                    log.debug("Updated label for company product {} to: {}", productId, label);
                }
            } catch (Exception e) {
                // Field not present or accessible, skip
            }

            // Update description if provided
            try {
                Object description = requestClass.getMethod("description").invoke(updateRequest);
                if (description != null && !((String) description).trim().isEmpty()) {
                    companyProduct.setDescription((String) description);
                    log.debug("Updated description for company product {}", productId);
                }
            } catch (Exception e) {
                // Field not present or accessible, skip
            }

            // Update selling price if provided
            try {
                Object sellingPrice = requestClass.getMethod("sellingPrice").invoke(updateRequest);
                if (sellingPrice != null) {
                    companyProduct.setSellingPrice((BigDecimal) sellingPrice);
                    log.debug("Updated selling price for company product {} to: {}", productId, sellingPrice);
                }
            } catch (Exception e) {
                // Field not present or accessible, skip
            }

            // Update delivery time if provided
            try {
                Object deliveryTime = requestClass.getMethod("averageDeliveryTime").invoke(updateRequest);
                if (deliveryTime != null) {
                    companyProduct.setAverageDeliveryTime((Integer) deliveryTime);
                    log.debug("Updated delivery time for company product {} to: {}", productId, deliveryTime);
                }
            } catch (Exception e) {
                // Field not present or accessible, skip
            }

            // Update image URL if provided
            try {
                Object imageUrl = requestClass.getMethod("imageUrl").invoke(updateRequest);
                if (imageUrl != null) {
                    // Update the base product's image URL since it's shared across suppliers
                    Product baseProduct = companyProduct.getProduct();
                    baseProduct.setImageUrl((String) imageUrl);
                    productRepository.save(baseProduct);
                    log.debug("Updated image URL for product {} to: {}", productId, imageUrl);
                }
            } catch (Exception e) {
                // Field not present or accessible, skip
            }

        } catch (Exception e) {
            log.error("Error updating company product: {}", e.getMessage());
            throw new RuntimeException("Failed to update company product: " + e.getMessage());
        }

        CompanyProduct savedProduct = companyProductRepository.save(companyProduct);
        log.info("Successfully updated company product: {}", productId);

        return savedProduct;
    }

    /**
     * Update a company product's selling price.
     *
     * @param productId The company product ID
     * @param newSellingPrice The new selling price
     * @return The updated company product
     * @throws ResourceNotFoundException if the product doesn't exist
     */
    @Transactional
    public CompanyProduct updateSellingPrice(UUID productId, BigDecimal newSellingPrice) {
        CompanyProduct companyProduct = companyProductRepository.findById(productId)
            .orElseThrow(() -> {
                log.error("Company product not found with ID: {}", productId);
                return new ResourceNotFoundException("Company product not found with ID: " + productId);
            });

        log.info("Updating selling price for company product '{}' from {} to {}",
            companyProduct.getLabel(), companyProduct.getSellingPrice(), newSellingPrice);

        companyProduct.setSellingPrice(newSellingPrice);
        return companyProductRepository.save(companyProduct);
    }

    /**
     * Delete a company product from inventory.
     * This removes the company's inventory record but preserves the base product.
     *
     * @param productId The company product ID
     * @throws ResourceNotFoundException if the product doesn't exist
     */
    @Transactional
    public void deleteCompanyProduct(UUID productId) {
        CompanyProduct companyProduct = companyProductRepository.findById(productId)
            .orElseThrow(() -> {
                log.error("Company product not found with ID: {}", productId);
                return new ResourceNotFoundException("Company product not found with ID: " + productId);
            });

        log.info("Deleting company product '{}' from inventory", companyProduct.getLabel());

        // Only delete the company product record, not the base product
        // The base product should remain for historical data and supplier references
        companyProductRepository.delete(companyProduct);

        log.info("Successfully deleted company product '{}' from inventory", companyProduct.getLabel());
    }

    /**
     * Get inventory statistics.
     *
     * @return Map containing inventory statistics
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getInventoryStatistics() {
        long totalProducts = companyProductRepository.count();
        long inStockProducts = companyProductRepository.countInStock();
        BigDecimal totalInventoryValue = companyProductRepository.calculateTotalInventoryValue();

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalProducts", totalProducts);
        stats.put("inStockProducts", inStockProducts);
        stats.put("outOfStockProducts", totalProducts - inStockProducts);
        stats.put("totalInventoryValue", totalInventoryValue);

        return stats;
    }

    /**
     * Convert CompanyProduct entity to DTO using the mapper.
     *
     * @param companyProduct The company product entity
     * @return The company product DTO
     */
    public CompanyProductDTO toDTO(CompanyProduct companyProduct) {
        return companyProductMapper.toDTO(companyProduct);
    }

    /**
     * Get total product count.
     *
     * @return Total number of company products
     */
    @Transactional(readOnly = true)
    public long getTotalProductCount() {
        return companyProductRepository.count();
    }

    /**
     * Get in-stock product count.
     *
     * @return Number of company products with stock > 0
     */
    @Transactional(readOnly = true)
    public long getInStockProductCount() {
        return companyProductRepository.countInStock();
    }

    /**
     * Get total inventory value.
     *
     * @return Total value of company inventory
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalInventoryValue() {
        return companyProductRepository.calculateTotalInventoryValue();
    }
}
