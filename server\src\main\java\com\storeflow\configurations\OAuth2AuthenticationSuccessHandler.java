package com.storeflow.configurations;

import com.storeflow.enums.Role;
import com.storeflow.models.Client;
import com.storeflow.models.User;
import com.storeflow.repositories.ClientRepository;
import com.storeflow.repositories.UserRepository;
import com.storeflow.enums.TokenType;
import com.storeflow.models.Token;
import com.storeflow.repositories.TokenRepository;
import com.storeflow.services.FileService;
import com.storeflow.services.JwtService;
import com.storeflow.utils.SecurePasswordGenerator;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class OAuth2AuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    private final UserRepository userRepository;
    private final ClientRepository clientRepository;
    private final TokenRepository tokenRepository;
    private final JwtService jwtService;
    private final FileService fileService;
    private final PasswordEncoder passwordEncoder;
    private final SecurePasswordGenerator securePasswordGenerator;

    @Value("${application.frontend.url}")
    private String frontendUrl;

    @Override
    @Transactional
    public void onAuthenticationSuccess(
        HttpServletRequest request,
        HttpServletResponse response,
        Authentication authentication
    ) throws IOException {
        try {
            log.info("Starting OAuth2 authentication success handler");

            if (!(authentication.getPrincipal() instanceof OAuth2User)) {
                throw new RuntimeException("Invalid OAuth2 authentication principal type");
            }

            OAuth2User oAuth2User = (OAuth2User) authentication.getPrincipal();

            // Extract user information from Google OAuth2User
            String email = oAuth2User.getAttribute("email");
            String firstName = oAuth2User.getAttribute("given_name");
            String lastName = oAuth2User.getAttribute("family_name");
            String googleId = oAuth2User.getAttribute("sub");
            String profilePictureUrl = oAuth2User.getAttribute("picture");

            log.info("OAuth2 authentication success for user: {}", email != null ? email : "unknown");
            log.debug("OAuth2 user attributes - email: {}, googleId: {}, firstName: {}, lastName: {}",
                     email, googleId, firstName, lastName);

            if (email == null || email.trim().isEmpty()) {
                throw new RuntimeException("Email is required from Google OAuth response");
            }

            if (googleId == null || googleId.trim().isEmpty()) {
                throw new RuntimeException("Google ID is required from OAuth response");
            }

            // Check if user already exists by Google ID
            User existingUser = userRepository.findByGoogleId(googleId).orElse(null);

            String accessToken;
            String refreshToken;
            String message;
            boolean requiresPhoneNumber;

            if (existingUser != null) {
                log.info("Found existing user with Google ID: {}", googleId);

                // Update profile picture if it's still a Google URL (not downloaded to our storage)
                if (profilePictureUrl != null && !profilePictureUrl.trim().isEmpty() &&
                    existingUser.getProfilePictureUrl() != null &&
                    existingUser.getProfilePictureUrl().contains("googleusercontent.com")) {
                    try {
                        log.info("Updating Google profile picture for existing user: {}", email);
                        String newProfilePictureUrl = fileService.downloadAndUploadGoogleProfilePicture(profilePictureUrl);
                        existingUser.setProfilePictureUrl(newProfilePictureUrl);
                        userRepository.save(existingUser);
                        log.info("Successfully updated profile picture for existing user");
                    } catch (Exception e) {
                        log.warn("Failed to update profile picture for existing user: {}", e.getMessage());
                    }
                }

                // User exists, check if phone number is complete
                if (existingUser.getPhoneNumber() == null || existingUser.getPhoneNumber().trim().isEmpty()) {
                    log.info("Existing user needs to complete phone number");
                    // Generate temporary tokens for phone completion
                    accessToken = jwtService.generateToken(existingUser);
                    refreshToken = jwtService.generateRefreshToken(existingUser);

                    // Save the access token to database for authentication
                    revokeAllUserTokens(existingUser);
                    saveUserToken(existingUser, accessToken);

                    requiresPhoneNumber = true;
                    message = "Welcome back! Please complete your profile by adding your phone number";
                } else {
                    log.info("Existing user login successful");
                    // User is complete, proceed with normal login
                    accessToken = jwtService.generateToken(existingUser);
                    refreshToken = jwtService.generateRefreshToken(existingUser);

                    // Save the access token to database for authentication
                    revokeAllUserTokens(existingUser);
                    saveUserToken(existingUser, accessToken);

                    requiresPhoneNumber = false;
                    message = "Welcome back! You're now signed in";
                }
            } else {
                log.info("No existing user found with Google ID, checking email: {}", email);
                // Check if user exists by email (from regular registration)
                User existingEmailUser = userRepository.findByEmail(email).orElse(null);
                if (existingEmailUser != null) {
                    log.warn("User with email {} already exists but without Google ID", email);
                    throw new RuntimeException("An account with this email already exists. Please sign in using your email and password instead.");
                }

                log.info("New Google OAuth user - redirecting to phone completion without saving to database");

                // DO NOT SAVE USER TO DATABASE YET!
                // User will be saved only after phone number is provided
                // We'll pass the Google OAuth data in the redirect URL for temporary storage

                requiresPhoneNumber = true;
                message = "Welcome to StoreFlow! Please add your phone number to complete your account setup";

                // We don't generate tokens yet since user is not saved to database
                // The phone completion will handle user creation and token generation
                accessToken = "TEMP_GOOGLE_OAUTH"; // Temporary placeholder
                refreshToken = "TEMP_GOOGLE_OAUTH"; // Temporary placeholder
            }

            // Validate tokens were generated successfully (only for existing users)
            if (!requiresPhoneNumber) {
                if (accessToken == null || accessToken.trim().isEmpty()) {
                    throw new RuntimeException("Failed to generate access token");
                }
                if (refreshToken == null || refreshToken.trim().isEmpty()) {
                    throw new RuntimeException("Failed to generate refresh token");
                }
            }

            // Encode the response data for URL parameters
            String encodedAccessToken = accessToken != null ? URLEncoder.encode(accessToken, StandardCharsets.UTF_8) : "";
            String encodedRefreshToken = refreshToken != null ? URLEncoder.encode(refreshToken, StandardCharsets.UTF_8) : "";
            String encodedMessage = URLEncoder.encode(message, StandardCharsets.UTF_8);

            log.info("OAuth2 flow decision - requiresPhoneNumber: {}, message: {}", requiresPhoneNumber, message);

            if (requiresPhoneNumber) {
                // For new users, pass Google OAuth data in URL for phone completion
                String encodedFirstName = URLEncoder.encode(firstName != null ? firstName : "", StandardCharsets.UTF_8);
                String encodedLastName = URLEncoder.encode(lastName != null ? lastName : "", StandardCharsets.UTF_8);
                String encodedEmail = URLEncoder.encode(email, StandardCharsets.UTF_8);
                String encodedGoogleId = URLEncoder.encode(googleId, StandardCharsets.UTF_8);
                String encodedProfilePicture = URLEncoder.encode(profilePictureUrl != null ? profilePictureUrl : "", StandardCharsets.UTF_8);

                String redirectUrl = String.format(
                    "%s/auth/complete-google-registration?firstName=%s&lastName=%s&email=%s&googleId=%s&profilePicture=%s&message=%s",
                    frontendUrl, encodedFirstName, encodedLastName, encodedEmail, encodedGoogleId, encodedProfilePicture, encodedMessage
                );
                log.info("Redirecting NEW USER to phone completion with Google data: {}", redirectUrl);
                response.sendRedirect(redirectUrl);
            } else {
                // Redirect to dashboard with tokens
                String redirectUrl = String.format(
                    "%s/auth/oauth2/success?accessToken=%s&refreshToken=%s&message=%s",
                    frontendUrl, encodedAccessToken, encodedRefreshToken, encodedMessage
                );
                log.info("Redirecting EXISTING USER to OAuth success: {}", redirectUrl);
                response.sendRedirect(redirectUrl);
            }

            log.info("OAuth2 authentication success handler completed successfully for user: {}", email);

        } catch (Exception e) {
            log.error("Error in OAuth2 authentication success handler: {}", e.getMessage(), e);

            // Provide more specific error messages
            String errorMessage = e.getMessage();
            if (errorMessage == null || errorMessage.trim().isEmpty()) {
                errorMessage = "OAuth2 authentication failed. Please try again.";
            }

            try {
                String encodedErrorMessage = URLEncoder.encode(errorMessage, StandardCharsets.UTF_8);
                String redirectUrl = String.format(
                    "%s/auth/oauth2/error?error=%s",
                    frontendUrl, encodedErrorMessage
                );
                log.info("Redirecting to OAuth error page: {}", redirectUrl);
                response.sendRedirect(redirectUrl);
            } catch (Exception redirectException) {
                log.error("Failed to redirect to error page: {}", redirectException.getMessage(), redirectException);
                // Fallback: send a simple error response
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("OAuth2 authentication failed");
            }
        }
    }

    /**
     * Token management methods to avoid circular dependency with AuthenticationService
     */
    private void revokeAllUserTokens(User user) {
        var validUserTokens = tokenRepository.findAllValidTokensByUser(user.getId());
        if (validUserTokens.isEmpty()) return;
        validUserTokens.forEach(token -> {
            token.setExpired(true);
            token.setRevoked(true);
        });
        tokenRepository.saveAll(validUserTokens);
    }

    private void saveUserToken(User user, String jwtToken) {
        var token = Token.builder()
            .user(user)
            .token(jwtToken)
            .tokenType(TokenType.BEARER)
            .revoked(false)
            .expired(false)
            .build();
        tokenRepository.save(token);
    }
}
