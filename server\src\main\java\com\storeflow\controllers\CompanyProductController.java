package com.storeflow.controllers;

import com.storeflow.DTOs.CompanyProductDTO;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.CompanyProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * Controller for managing company products (internal inventory).
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/company-products")
public class CompanyProductController {

    private final CompanyProductService companyProductService;

    /**
     * Get all company products with pagination and search.
     *
     * @param query Optional search query
     * @param page Page number (0-based)
     * @param size Page size
     * @param sortBy Field to sort by
     * @param sortDir Sort direction ('asc' or 'desc')
     * @return A page of company products
     */
    @GetMapping
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<Page<CompanyProductDTO>>> getAllCompanyProducts(
        @RequestParam(required = false, defaultValue = "") String query,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "label") String sortBy,
        @RequestParam(defaultValue = "asc") String sortDir
    ) {
        log.info("Received request to get company products with query: '{}', page: {}, size: {}", 
            query, page, size);

        // Create sort object
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        // Get products with pagination and search
        Page<CompanyProductDTO> products;

        if (query != null && !query.trim().isEmpty()) {
            products = companyProductService.searchProducts(query, pageable)
                .map(companyProductService::toDTO);
        } else {
            products = companyProductService.getAllProducts(pageable)
                .map(companyProductService::toDTO);
        }

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Company products retrieved successfully",
            products
        ));
    }

    /**
     * Get company inventory statistics.
     *
     * @return Inventory statistics
     */
    @GetMapping("/stats")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<InventoryStats>> getInventoryStats() {
        log.info("Received request to get company inventory statistics");

        long totalProducts = companyProductService.getTotalProductCount();
        long inStockProducts = companyProductService.getInStockProductCount();
        BigDecimal totalValue = companyProductService.getTotalInventoryValue();

        InventoryStats stats = new InventoryStats(totalProducts, inStockProducts, totalValue);

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Inventory statistics retrieved successfully",
            stats
        ));
    }

    /**
     * Get a single company product by ID.
     *
     * @param productId The company product ID
     * @return The company product details
     */
    @GetMapping("/{productId}")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<CompanyProductDTO>> getCompanyProduct(@PathVariable UUID productId) {
        log.info("Received request to get company product with ID: {}", productId);

        try {
            CompanyProductDTO product = companyProductService.toDTO(
                companyProductService.getCompanyProductById(productId)
            );

            return ResponseEntity.ok(new ApiResponse<>(
                true,
                "Company product retrieved successfully",
                product
            ));
        } catch (Exception e) {
            log.error("Error retrieving company product with ID: {}", productId, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Update the selling price of a company product.
     *
     * @param productId The company product ID
     * @param request The price update request
     * @return The updated company product
     */
    @PutMapping("/{productId}/price")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<CompanyProductDTO>> updateProductPrice(
        @PathVariable UUID productId,
        @RequestBody PriceUpdateRequest request
    ) {
        log.info("Received request to update price for company product: {} to {}", productId, request.sellingPrice());

        try {
            CompanyProductDTO updatedProduct = companyProductService.toDTO(
                companyProductService.updateSellingPrice(productId, request.sellingPrice())
            );

            return ResponseEntity.ok(new ApiResponse<>(
                true,
                "Product price updated successfully",
                updatedProduct
            ));
        } catch (Exception e) {
            log.error("Error updating price for company product: {}", productId, e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                false,
                e.getMessage(),
                null
            ));
        }
    }

    /**
     * Update a company product with full details.
     *
     * @param productId The company product ID
     * @param request The product update request
     * @return The updated company product
     */
    @PutMapping("/{productId}")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<CompanyProductDTO>> updateCompanyProduct(
        @PathVariable UUID productId,
        @RequestBody CompanyProductUpdateRequest request
    ) {
        log.info("Received request to update company product: {} with data: {}", productId, request);

        try {
            CompanyProductDTO updatedProduct = companyProductService.toDTO(
                companyProductService.updateCompanyProduct(productId, request)
            );

            return ResponseEntity.ok(new ApiResponse<>(
                true,
                "Product updated successfully",
                updatedProduct
            ));
        } catch (Exception e) {
            log.error("Error updating company product: {}", productId, e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                false,
                e.getMessage(),
                null
            ));
        }
    }

    /**
     * Delete a company product from inventory.
     *
     * @param productId The company product ID
     * @return Success response
     */
    @DeleteMapping("/{productId}")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<Void>> deleteCompanyProduct(@PathVariable UUID productId) {
        log.info("Received request to delete company product: {}", productId);

        try {
            companyProductService.deleteCompanyProduct(productId);

            return ResponseEntity.ok(new ApiResponse<>(
                true,
                "Product deleted from inventory successfully",
                null
            ));
        } catch (Exception e) {
            log.error("Error deleting company product: {}", productId, e);
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                false,
                e.getMessage(),
                null
            ));
        }
    }

    /**
     * Record class for inventory statistics.
     */
    public record InventoryStats(
        long totalProducts,
        long inStockProducts,
        BigDecimal totalValue
    ) {}

    /**
     * Record class for price update requests.
     */
    public record PriceUpdateRequest(
        BigDecimal sellingPrice
    ) {}

    /**
     * Record class for company product update requests.
     */
    public record CompanyProductUpdateRequest(
        String label,
        String description,
        BigDecimal sellingPrice,
        Integer averageDeliveryTime,
        String imageUrl
    ) {}
}
