:root {
  body[data-scroll-locked][data-scroll-locked] {
    overflow: auto !important;
  }
  body[data-scroll-locked] {
    margin-right: 0 !important;
  }
}

html,
body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Imprima", sans-serif;
  @apply bg-inherit;
}

button {
  cursor: pointer;
}

/* Add these styles to your global CSS file */

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 8s ease infinite;
}

/* Custom Phone Input Styling */
.custom-phone-input {
  width: 100%;
  height: 100%;
  border: none !important;
  background: transparent !important;
  outline: none !important;
  font-size: 1rem;
  padding-left: 10px;
}

.custom-phone-input input {
  width: 100%;
  height: 100%;
  border: none !important;
  background: transparent !important;
  outline: none !important;
  padding-left: 8px;
  color: #334155;
}

.PhoneInputCountry {
  margin-left: 10px;
  margin-right: 6px;
}

/* Hover effects for cards */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* Pulse animation for the avatar */
@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.avatar-pulse {
  animation: pulse-border 2s infinite;
}

/* Progress bar animation */
@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.animate-progress {
  animation: progress 1s ease-out forwards;
}

/* Improved focus styles */
.focus-ring {
  transition: all 0.2s ease;
}

.focus-ring:focus-within {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Custom scrollbar styles */
.scrollbar-custom {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.5) transparent;
}

.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: rgba(203, 213, 225, 0.5);
  border-radius: 8px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.7);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  -webkit-box-shadow: transparent inset; /* Custom background color */
  -webkit-text-fill-color: #455a64; /* Custom text color */
  font-family: "Outfit", sans-serif !important;
  caret-color: #455a64; /* Custom caret/cursor color */
  transition: background-color 5000s ease-in-out 0s; /* Long transition to prevent flashing */
}

/* Hide the password reveal button in Microsoft Edge */
input[type="password"]::-ms-reveal {
  display: none;
}

/* Hide the password reveal button in WebKit-based browsers (Chrome, Safari, Opera) */
input[type="password"]::-webkit-credentials-auto-fill-button,
input[type="password"]::-webkit-search-decoration,
input[type="password"]::-webkit-outer-spin-button,
input[type="password"]::-webkit-inner-spin-button,
input[type="password"]::-webkit-textfield-decoration-container {
  display: none;
  -webkit-appearance: none;
}

/* General reset for appearance */
input[type="password"] {
  -webkit-appearance: none;
  appearance: none;
}

/* Customize react-phone-number-input */

/* Container styling */
.custom-phone-input {
  @apply h-[53px] w-full bg-transparent text-base lg:text-lg border-none pl-0;
  --PhoneInput-color--focus: #475569; /* blue-gray-800 */
  --PhoneInputCountryFlag-height: 20px;
}

/* Flag dropdown button styling */
.custom-phone-input .PhoneInputCountryIcon {
  @apply ml-[6px];
}

.employee-phone-input .PhoneInputCountryIcon {
  @apply ml-[10px];
}

.custom-phone-input .PhoneInputCountrySelectArrow {
  @apply opacity-100 text-blue-gray-500;
}

/* Input field styling */
.PhoneInput input {
  @apply h-full border-none outline-none pr-[12px] pl-[1px] bg-transparent text-base lg:text-lg text-blue-gray-700 focus:text-blue-gray-700 transition-all duration-300;
}

.employee-phone-input input {
  @apply text-sm pl-0;
}

/* When dropdown is open */
.PhoneInput--focus input,
.PhoneInput input:focus {
  @apply text-blue-gray-700;
}

/* Focusing states */
.custom-phone-input .PhoneInputInput:focus {
  @apply outline-none;
}

.custom-phone-input .PhoneInputInput::placeholder {
  @apply text-blue-gray-500;
}

/* Country dropdown styling */
.PhoneInputCountrySelect {
  @apply relative;
}

/* Country dropdown styling with rounded corners */
.PhoneInputCountrySelectDropdown {
  @apply !rounded-xl overflow-hidden;
}

/* Individual country items in the dropdown */
.PhoneInputCountrySelectOption {
  @apply transition-colors;
}

/* Highlighted/selected country in the dropdown */
.PhoneInputCountrySelectOption--highlight {
  @apply bg-blue-gray-100;
}

/* Style tabs for premium look */
.hidden-tabs [role="tablist"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  height: 0;
  overflow: hidden;
}

/* Custom premium tabs styling */
.premium-tabs-container {
  @apply bg-gradient-to-r from-slate-50/90 to-blue-gray-50/80 rounded-full p-1.5 shadow-sm border border-blue-gray-100/80 backdrop-blur-sm;
  transition: all 0.3s ease;
}

.premium-tabs-container:hover {
  @apply shadow-md border-blue-gray-200/90;
}

/* Premium tab button styling */
.premium-tab-button {
  @apply flex items-center justify-center gap-2 py-2.5 px-4 rounded-full transition-all;
}

.premium-tab-button.active {
  @apply bg-white shadow-sm text-blue-gray-800;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.premium-tab-button:not(.active) {
  @apply text-blue-gray-600 hover:text-blue-gray-800;
}

.premium-tab-button:hover:not(.active) {
  @apply bg-white/50;
}

.premium-tab-button .tab-icon {
  @apply size-[22px] transition-all duration-300;
}

.premium-tab-button:hover .tab-icon {
  transform: translateY(-1px) scale(1.05);
}

.premium-tab-button.active .tab-icon {
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
}

.premium-tab-button .tab-text {
  @apply hidden md:inline font-medium transition-all;
  letter-spacing: 0.01em;
}
