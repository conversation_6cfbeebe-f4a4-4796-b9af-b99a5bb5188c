package com.storeflow.services;

import com.storeflow.DTOs.CatalogProductDTO;
import com.storeflow.models.CompanyProduct;
import com.storeflow.repositories.CompanyProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service for managing the product catalog available to clients.
 * This service provides products from the company inventory (CompanyProduct) to clients.
 * Clients see products that admin/employees have added to inventory, excluding purchase prices.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CatalogService {

    private final CompanyProductRepository companyProductRepository;

    /**
     * Get all available products in the catalog with pagination.
     * Products come from the company inventory (CompanyProduct) managed by admin/employees.
     * Includes products with zero stock to show them as out-of-stock.
     *
     * @param pageable Pagination information
     * @return A page of catalog products
     */
    public Page<CatalogProductDTO> getAvailableProducts(Pageable pageable) {
        log.info("Fetching available products for catalog from company inventory");

        try {
            // Get all company products (including those with zero stock)
            Page<CompanyProduct> companyProducts = companyProductRepository.findAll(pageable);

            log.info("Found {} company products", companyProducts.getTotalElements());

            // Convert to catalog products
            Page<CatalogProductDTO> catalogProducts = companyProducts.map(this::convertToCatalogProduct);

            log.info("Generated {} catalog products", catalogProducts.getTotalElements());

            return catalogProducts;
        } catch (Exception e) {
            log.error("Error fetching available products for catalog", e);
            // Return empty page on error to prevent complete failure
            return Page.empty(pageable);
        }
    }

    /**
     * Get a specific product from the catalog by label.
     * Now includes products with zero available stock to show them as out-of-stock.
     *
     * @param label The product label
     * @return The catalog product or null if not found
     */
    public CatalogProductDTO getProductByLabel(String label) {
        log.info("Fetching catalog product by label: {}", label);

        try {
            Optional<CompanyProduct> companyProductOpt = companyProductRepository.findByLabel(label);

            if (companyProductOpt.isEmpty()) {
                log.warn("No company product found for label: {}", label);
                return null;
            }

            return convertToCatalogProduct(companyProductOpt.get());
        } catch (Exception e) {
            log.error("Error fetching catalog product by label: {}", label, e);
            return null;
        }
    }

    /**
     * Convert a CompanyProduct to a CatalogProductDTO.
     * This method excludes purchase prices (private to admin/employees) and shows selling prices.
     *
     * @param companyProduct The company product to convert
     * @return The catalog product DTO
     */
    private CatalogProductDTO convertToCatalogProduct(CompanyProduct companyProduct) {
        return new CatalogProductDTO(
            companyProduct.getLabel(),
            companyProduct.getDescription(),
            companyProduct.getProduct().getImageUrl(),
            companyProduct.getProduct().getBrand(),
            companyProduct.getSellingPrice(), // Show selling price to clients
            companyProduct.getStockQuantity(),
            companyProduct.getAverageDeliveryTime()
        );
    }
}
