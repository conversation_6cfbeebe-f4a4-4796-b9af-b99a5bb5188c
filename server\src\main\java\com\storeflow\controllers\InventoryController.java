package com.storeflow.controllers;

import com.storeflow.DTOs.CompanyProductDTO;
import com.storeflow.responses.ApiResponse;
import com.storeflow.mappers.CompanyProductMapper;
import com.storeflow.models.CompanyProduct;
import com.storeflow.services.CompanyProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;

/**
 * Controller for inventory management operations.
 * Accessible by ADMIN and EMPLOYEE roles.
 */
@RestController
@RequestMapping("/api/v1/inventory")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN') or hasRole('EMPLOYEE')")
public class InventoryController {

    private final CompanyProductService companyProductService;
    private final CompanyProductMapper companyProductMapper;

    /**
     * Get all company products with pagination and search.
     *
     * @param page The page number (0-based)
     * @param size The page size
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc/desc)
     * @param search The search term (optional)
     * @return Page of company products
     */
    @GetMapping("/products")
    public ResponseEntity<ApiResponse<Page<CompanyProductDTO>>> getInventoryProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "12") int size,
            @RequestParam(defaultValue = "label") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) String search) {

        log.info("Fetching inventory products - page: {}, size: {}, sortBy: {}, sortDir: {}, search: '{}'",
            page, size, sortBy, sortDir, search);

        try {
            Sort sort = Sort.by(sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC, sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<CompanyProduct> companyProducts;
            if (search != null && !search.trim().isEmpty()) {
                companyProducts = companyProductService.searchProducts(search, pageable);
            } else {
                companyProducts = companyProductService.getAllProducts(pageable);
            }

            Page<CompanyProductDTO> productDTOs = companyProducts.map(companyProductMapper::toDTO);

            return ResponseEntity.ok(ApiResponse.<Page<CompanyProductDTO>>builder()
                .success(true)
                .message("Inventory products retrieved successfully")
                .data(productDTOs)
                .build());

        } catch (Exception e) {
            log.error("Error fetching inventory products", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.<Page<CompanyProductDTO>>builder()
                    .success(false)
                    .message("Failed to fetch inventory products")
                    .data(null)
                    .build());
        }
    }

    /**
     * Get inventory statistics.
     *
     * @return Inventory statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getInventoryStatistics() {
        log.info("Fetching inventory statistics");

        try {
            Map<String, Object> statistics = companyProductService.getInventoryStatistics();

            return ResponseEntity.ok(ApiResponse.<Map<String, Object>>builder()
                .success(true)
                .message("Inventory statistics retrieved successfully")
                .data(statistics)
                .build());

        } catch (Exception e) {
            log.error("Error fetching inventory statistics", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.<Map<String, Object>>builder()
                    .success(false)
                    .message("Failed to fetch inventory statistics")
                    .data(null)
                    .build());
        }
    }

    /**
     * Update the selling price of a company product.
     *
     * @param productId The company product ID
     * @param request The price update request
     * @return Updated company product
     */
    @PutMapping("/products/{productId}/price")
    public ResponseEntity<ApiResponse<CompanyProductDTO>> updateProductPrice(
            @PathVariable UUID productId,
            @RequestBody PriceUpdateRequest request) {

        log.info("Updating price for company product {} to {}", productId, request.sellingPrice());

        try {
            CompanyProduct updatedProduct = companyProductService.updateSellingPrice(
                productId, request.sellingPrice());
            
            CompanyProductDTO productDTO = companyProductMapper.toDTO(updatedProduct);

            return ResponseEntity.ok(ApiResponse.<CompanyProductDTO>builder()
                .success(true)
                .message("Product price updated successfully")
                .data(productDTO)
                .build());

        } catch (Exception e) {
            log.error("Error updating product price", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.<CompanyProductDTO>builder()
                    .success(false)
                    .message(e.getMessage())
                    .data(null)
                    .build());
        }
    }

    /**
     * Get a single company product by ID.
     *
     * @param productId The company product ID
     * @return Company product details
     */
    @GetMapping("/products/{productId}")
    public ResponseEntity<ApiResponse<CompanyProductDTO>> getInventoryProduct(@PathVariable UUID productId) {
        log.info("Fetching company product: {}", productId);

        try {
            CompanyProduct companyProduct = companyProductService.getCompanyProductById(productId);
            CompanyProductDTO productDTO = companyProductMapper.toDTO(companyProduct);

            return ResponseEntity.ok(ApiResponse.<CompanyProductDTO>builder()
                .success(true)
                .message("Product retrieved successfully")
                .data(productDTO)
                .build());

        } catch (Exception e) {
            log.error("Error fetching company product", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.<CompanyProductDTO>builder()
                    .success(false)
                    .message(e.getMessage())
                    .data(null)
                    .build());
        }
    }

    /**
     * Delete a company product from inventory.
     *
     * @param productId The company product ID
     * @return Success response
     */
    @DeleteMapping("/products/{productId}")
    public ResponseEntity<ApiResponse<Void>> deleteInventoryProduct(@PathVariable UUID productId) {
        log.info("Deleting company product from inventory: {}", productId);

        try {
            companyProductService.deleteCompanyProduct(productId);

            return ResponseEntity.ok(ApiResponse.<Void>builder()
                .success(true)
                .message("Product removed from inventory successfully")
                .data(null)
                .build());

        } catch (Exception e) {
            log.error("Error deleting company product", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.<Void>builder()
                    .success(false)
                    .message(e.getMessage())
                    .data(null)
                    .build());
        }
    }

    /**
     * Request DTO for price updates.
     */
    public record PriceUpdateRequest(BigDecimal sellingPrice) {}
}
